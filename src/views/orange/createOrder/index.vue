<template>
  <div class="app-container">
    <div style="margin-bottom: 10px; background: #f5f5f5; padding: 32px">
      <el-form :model="form" label-width="120px" :rules="rules" ref="form">
        <el-form-item label="支付渠道">
          <el-radio-group v-model="form.payChannel">
            <el-radio label="YIBEIJIA_SCAN">益倍嘉-扫码付</el-radio>
            <el-radio label="BAOFOO_TRANS_PAY">宝付</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="借款号" prop="orderId">
          <el-input v-model="form.orderId"></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="productChannel">
          <el-select placeholder="请选择" style="width: 200px" v-model="form.productChannel" clearable>
            <el-option v-for="item in productChannelOptions" :label="item.label" :value="item.value"
              :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="form.mobile"></el-input>
        </el-form-item>

        <template v-if="form.payChannel === 'BAOFOO_TRANS_PAY'">
          <el-form-item label="银行卡号" prop="payerAcctCode">
            <el-input v-model="form.payerAcctCode"></el-input>
          </el-form-item>
          <el-form-item label="银行卡姓名" prop="payerUserName">
            <el-input v-model="form.payerUserName"></el-input>
          </el-form-item>
        </template>

        <el-form-item label="还款类型" prop="repayPurpose">
          <el-radio-group v-model="form.repayPurpose">
            <el-radio label="CURRENT">当期</el-radio>
            <el-radio label="CLEAR">结清</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="期数" prop="period">
          <el-select placeholder="请选择" style="width: 110px" v-model="form.period" clearable>
            <el-option v-for="item in periodOptions" :label="item" :value="item" :key="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="还款金额" prop="amount">
          <el-input v-model="form.amount"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button round type="primary" @click="submit">
            {{
        form.payChannel === "YIBEIJIA_SCAN" ? "生成支付单" : "去宝付提款"
      }}
          </el-button>
          <el-button round @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 二维码弹窗 -->
    <el-dialog title="普通支付" :visible.sync="dialogCodeVisible" width="370px" :before-close="closeCodeDialog">
      <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        ">
        <div style="margin-bottom: 10px">
          <span>普通支付金额: {{ codeRes.actAmount }}元</span>
        </div>
        <div style="margin-bottom: 10px">
          <span>订单号: {{ codeRes.orderId }}</span>
        </div>
        <div style="margin-bottom: 10px">
          <span>用户姓名: {{ codeRes.name }}</span>
        </div>
        <div style="margin-bottom: 10px">请将二维码截图给用户进行扫码</div>
        <div style="position: relative; width: 300px">
          <img src="@/assets/images/code_bg.png" alt="" style="width: 100%; height: auto" />
          <img :src="'data:image/png;base64,' + codeRes.qrCodeBase64" alt="" style="
              width: 200px;
              height: 200px;
              display: block;
              position: absolute;
              left: 50px;
              top: 110px;
            " />
        </div>
      </div>
      <div style="display: flex; justify-content: center">
        <el-button round type="primary" size="mini" @click="openSend">发送支付链接</el-button>
      </div>
    </el-dialog>

    <!-- 发送弹窗 -->
    <el-dialog title="确认弹窗" :visible.sync="dialogSendVisible" width="370px" :before-close="closeSendDialog">
      <div style="font-size: 14px; margin-bottom: 10px">
        输入手机号发送支付链接
      </div>
      <el-input style="margin-bottom: 16px" placeholder="请输入" v-model="phone" />
      <div style="display: flex; justify-content: flex-end">
        <el-button round type="primary" size="mini" @click="send">发送</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { aggregatePayApply, sendPayUrlSms } from "@/api/postLoanManage";

export default {
  name: "",
  data() {
    return {
      form: {
        payChannel: "YIBEIJIA_SCAN",
        orderId: undefined,
        repayPurpose: "CURRENT",
        period: null,
        amount: null, // 应还金额
        payerAcctCode: undefined,
        payerUserName: undefined,
      },
      periodOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      productChannelOptions: [
        {
          label: "新橙优品",
          value: "XCYP",
        },
        {
          label: "小闪分期",
          value: "XSFQ",
        },
        {
          label: "轻花优品",
          value: "QHYP_M",
        },
        {
          label: "极易购",
          value: "EASY_BUY",
        },
        {
          label: "测试",
          value: "TEST",
        },
      ],
      dialogCodeVisible: false,
      codeRes: {},
      dialogSendVisible: false,
      phone: "",

      rules: {
        orderId: [{ required: true, message: "不能为空", trigger: "blur" }],
        productChannel: [
          { required: true, message: "不能为空", trigger: "blur" },
        ],
        period: [{ required: true, message: "不能为空", trigger: "blur" }],
        amount: [
          { required: true, message: "不能为空", trigger: "blur" },
          { validator: this.checkNumber, trigger: 'blur' }
        ],
        name: [{ required: true, message: "不能为空", trigger: "blur" }],
        mobile: [
          { required: true, message: "不能为空", trigger: "blur" },
          { validator: this.checkPhone, trigger: "blur" },
        ],
        payerAcctCode: [
          { required: true, message: "不能为空", trigger: "blur" },
          { validator: this.checkInteger, trigger: "blur" },
        ],
        payerUserName: [
          { required: true, message: "不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    checkNumber(rule, value, callback) {
      const reg = /^(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/; // 正数且小数点后最多两位的正则表达式
      if (value === '' || reg.test(value)) {
        callback();
      } else {
        callback(new Error('请输入正数且小数点后最多两位的数字'));
      }
    },
    checkPhone(rule, value, callback) {
      const reg = /^1[0-9]{10}$/; // 手机号正则表达式
      if (value === "" || reg.test(value)) {
        callback();
      } else {
        callback(new Error("请输入有效的手机号码"));
      }
    },
    checkInteger(rule, value, callback) {
      const reg = /^[1-9]\d*$/;
      if (value === "" || reg.test(value)) {
        callback();
      } else {
        callback(new Error("请输入正整数"));
      }
    },
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let params = {
            ...this.form,
            reduceAmount: 0,
            actAmount: this.form.amount,
            // payChannel:'ZHONGJIN',
            // productChannel:'XCYP',
          };

          aggregatePayApply(params).then((res) => {
            if (this.form.payChannel === "YIBEIJIA_SCAN") {
              this.codeRes = res.data;
              this.dialogCodeVisible = true;
            } else if (this.form.payChannel === "BAOFOO_TRANS_PAY") {
              this.$message.success("操作成功");
              this.reset();
            }
          });
        }
      });
    },

    reset() {
      this.$refs["form"].resetFields();
    },

    closeCodeDialog() {
      this.dialogCodeVisible = false;
    },

    openSend() {
      this.phone = this.form.mobile;
      this.dialogSendVisible = true;
    },
    closeSendDialog() {
      this.dialogSendVisible = false;
    },

    // 发送
    send() {
      if (!this.phone) {
        this.$message.error("请输入手机号码");
        return;
      }

      let params = {
        mobile: this.phone,
        aggregatePayId: this.codeRes.aggregatePayId,
      };

      sendPayUrlSms(params).then((res) => {
        this.$message.success("发送成功");
        this.closeSendDialog();
      });
    },
  },
};
</script>
