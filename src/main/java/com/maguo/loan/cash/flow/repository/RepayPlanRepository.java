package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.RepayState;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface RepayPlanRepository extends JpaRepository<RepayPlan, String> {

    // 获取指定时间段内所有还款计划
    List<RepayPlan> findByPlanRepayDate(LocalDate localDate);

    // 根据plan_repay_date大于当前传入的日期 获取数据
    List<RepayPlan> findByPlanRepayDateGreaterThanEqual(LocalDate localDate);
//    List<RepayPlan> findByActRepayTimeBetween(LocalDateTime start, LocalDateTime end);

    List<RepayPlan> findByActRepayTimeBetween(LocalDateTime yesterdayStart, LocalDateTime yesterdayEnd);

    List<RepayPlan> findByPlanRepayDateAndCustRepayState(LocalDate planRepayDate, RepayState custRepayState);

    RepayPlan findFirstByLoanIdAndPlanRepayDateBeforeAndCustRepayStateOrderByPeriodAsc(String loanId, LocalDate planRepayDate, RepayState custRepayState);

    RepayPlan findFirstByLoanIdAndCustRepayStateOrderByPeriodAsc(String loanId, RepayState custRepayState);

    List<RepayPlan> findByPlanRepayDateBeforeAndCustRepayState(LocalDate planRepayDate, RepayState custRepayState, Pageable pageable);

    List<RepayPlan> findByLoanIdAndCustRepayStateOrderByPeriodAsc(String loanId, RepayState custRepayState);

    Boolean existsAllByLoanIdAndCustRepayStateOrderByPeriodAsc(String loanId, RepayState custRepayState);

    List<RepayPlan> findByLoanIdAndCustRepayState(String loanId, RepayState custRepayState);

    RepayPlan findByLoanIdAndPeriod(String loanId, Integer period);

    RepayPlan findByLoanIdAndPeriodAndCustRepayState(String loanId, Integer period, RepayState repayState);

    List<RepayPlan> findByLoanIdAndPeriodGreaterThanEqual(String loanId, Integer period);

    List<RepayPlan> findByLoanIdOrderByPeriod(String loanId);

    /**
     * 根据创建时间查询还款计划
     *
     * @param startDay 开始时间
     * @param endDay   结束时间
     */
    List<RepayPlan> findAllByCreatedTimeBetweenOrderByLoanIdAscPeriodAsc(LocalDateTime startDay, LocalDateTime endDay);

    /**
     * 根据更新时间查询还款计划
     *
     * @param startDay 开始时间
     * @param endDay   结束时间
     */
    List<RepayPlan> findAllByUpdatedTimeBetweenOrderByLoanIdAscPeriodAsc(LocalDateTime startDay, LocalDateTime endDay);

    List<RepayPlan> findByLoanIdAndCustRepayStateOrderByPlanRepayDateAsc(String loanId, RepayState custRepayState);

    List<RepayPlan> findByLoanId(String id);

    @Query(value = "select  l.id , l.bank_channel from loan l where l.loan_state = 'SUCCEED' "
        + "and exists (select 1 from repay_plan r where  r.loan_id = l.id and r.plan_repay_date < ?1 and r.cust_repay_state = ?2)",
        nativeQuery = true)
    List<Map<String, String>> findAllShouldRepay(LocalDate planRepayDate, String custRepayState);

    @Query(value = "select  l.id , l.bank_channel from loan l where l.loan_state = 'SUCCEED' and l.bank_channel in ?3 "
        + "and exists (select 1 from repay_plan r where  r.loan_id = l.id and r.plan_repay_date < ?1 and r.cust_repay_state = ?2)",
        nativeQuery = true)
    List<Map<String, String>> findAllShouldRepayByBankChannels(LocalDate planRepayDate, String custRepayState, List<String> bankChannels);

    List<RepayPlan> findByLoanIdIn(Set<String> strings);

    @Query(value = "select "
        + "zcar.apply_seq as ysxApplSeq,"
        + "o.id as loanNo,"
        + "'NORM' as loanDebtSts,"
        + "l.irr_rate as priceIntRat,"
        + "rp.period as psPerdNo,"
        + "rp.plan_repay_date as psDueDt,"
        + "rp.amount as setlInstmAmt,"
        + "rp.principal_amt as psPrcpAmt,"
        + "rp.interest_amt as psNormInt,"
        + "rp.guarantee_amt as feeSumAmt,"
        + "'0' as penalFeeAmt,"
        + "rp.penalty_amt as psOdIntAmt,"
        + "if(rp.act_principal_amt is null, 0,rp.act_principal_amt) as setlPrcp,"
        + "if(rp.act_interest_amt is null, 0, rp.act_interest_amt) as setlNormInt,"
        + "if(rp.act_guarantee_amt is null, 0, rp.act_guarantee_amt) as setlFeeAmt,"
        + "if(rp.act_breach_amt is null, 0, rp.act_breach_amt) as setlPenalAmt,"
        + "if(rp.act_penalty_amt is null, 0, rp.act_penalty_amt) as setlOdIntAmt,"
        + "'0' as graceDays,"
        + "'N' as setlInd,"
        + "'' as lastSetlDt,"
        + "'0' as psSts "
        + "from  loan l join repay_plan rp on rp.loan_id = l.id "
        + "join `order` o on l.order_id = o.id "
        + "join zycfc_credit_apply_record zcar on zcar.risk_id = o.risk_id "
        + "where o.call_zy_risk = 'Y' and l.flow_channel='ZYCFC' and "
        + "l.loan_time >= :beginTime and l.loan_time < :endTime "
        + "union all "
        + "select "
        + "zcar.apply_seq as ysxApplSeq,"
        + "o.id as loanNo,"
        + "if(o.order_state = 'CLEAR','SETL',if(datediff(rp.act_repay_time, rp.plan_repay_date)>0, 'DELQ', 'NORM')) as loanDebtSts,"
        + "l.irr_rate as priceIntRat,"
        + "rp.period as psPerdNo,"
        + "rp.plan_repay_date as psDueDt,"
        + "rp.amount as setlInstmAmt,"
        + "rp.principal_amt as psPrcpAmt,"
        + "rp.interest_amt as psNormInt,"
        + "rp.guarantee_amt as feeSumAmt,"
        + "'0' as penalFeeAmt,"
        + "rp.penalty_amt as psOdIntAmt,"
        + "if(rp.act_principal_amt is null, 0,rp.act_principal_amt) as setlPrcp,"
        + "if(rp.act_interest_amt is null, 0, rp.act_interest_amt) as setlNormInt,"
        + "if(rp.act_guarantee_amt is null, 0, rp.act_guarantee_amt) as setlFeeAmt,"
        + "if(rp.act_breach_amt is null, 0, rp.act_breach_amt) as setlPenalAmt,"
        + "if(rp.act_penalty_amt is null, 0, rp.act_penalty_amt) as setlOdIntAmt,"
        + "if(datediff(rp.act_repay_time, rp.plan_repay_date)<=0, 0, datediff(rp.act_repay_time, rp.plan_repay_date))  as graceDays,"
        + "'Y' as setlInd, "
        + "date_format(rp.act_repay_time ,'%Y-%m-%d') as lastSetlDt,"
        + "if(datediff(rp.act_repay_time, rp.plan_repay_date)<=0, '3', '1') as psSts "
        + "from repay_plan rp join loan l on rp.loan_id = l.id "
        + "join `order` o on  l.order_id = o.id "
        + "join zycfc_credit_apply_record zcar on zcar.risk_id = o.risk_id "
        + "where o.call_zy_risk = 'Y' and l.flow_channel='ZYCFC' "
        + "and rp.act_repay_time >= :startTime and rp.act_repay_time < :finishTime", nativeQuery = true)
    List<Map<String, String>> findLoanAndRepayFileUpload(LocalDateTime beginTime, LocalDateTime endTime, LocalDateTime startTime, LocalDateTime finishTime);

    int countByLoanId(String loanId);

    List<RepayPlan> findByUserIdAndCustRepayStateOrderByPeriodAsc(String userId, RepayState custRepayState);


    List<RepayPlan> findAllByLoanIdInAndCustRepayState(List<String> loanIdList, RepayState repayState);

    @Query(value = """
        SELECT DISTINCT loan_id FROM repay_plan
        WHERE loan_id > :lastLoanId OR :lastLoanId IS NULL
        ORDER BY loan_id
        LIMIT :batchSize
        """, nativeQuery = true)
    List<String> findDistinctLoanIds(String lastLoanId, int batchSize);

    @Query(value = """
        SELECT DISTINCT loan_id FROM repay_plan
        WHERE  plan_repay_date = :tomorrowDate
        AND (act_amount = 0 OR act_amount IS NULL)
        AND (loan_id > :lastLoanId OR :lastLoanId IS NULL)
        ORDER BY loan_id
        LIMIT :batchSize
        """, nativeQuery = true)
    List<String> findDistinctLoanId(String lastLoanId, LocalDate tomorrowDate, int batchSize);

    @Query(value = "SELECT " +
        "r.user_id as userId, " +
        "MAX(CASE WHEN r.cust_repay_state = 'NORMAL' AND r.plan_repay_date < CURRENT_DATE " +
        "THEN DATEDIFF(CURRENT_DATE, r.plan_repay_date) ELSE 0 END) AS currentOverdueDays, " +
        "MAX(CASE WHEN r.cust_repay_state = 'REPAID' " +
        "THEN DATEDIFF(r.act_repay_time, r.plan_repay_date) ELSE 0 END) AS historicalMaxOverdueDays " +
        "FROM repay_plan r " +
        "WHERE r.user_id = :userId " +
        "GROUP BY r.user_id", nativeQuery = true)
    Map<String, Object> findUserOverdueDays(@Param("userId") String userId);

    @Query(nativeQuery = true, value = """
        SELECT
            COALESCE(SUM(CASE WHEN plan_repay_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 3 MONTH)
                             AND cust_repay_state = 'NORMAL' THEN amount END), 0) AS debt_need_finish3m,
            COALESCE(SUM(CASE WHEN plan_repay_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 6 MONTH)
                             AND cust_repay_state = 'NORMAL' THEN amount END), 0) AS debt_need_finish6m,
            COALESCE(SUM(CASE WHEN plan_repay_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 12 MONTH)
                             AND cust_repay_state = 'NORMAL' THEN amount END), 0) AS debt_need_finish12m,
            COALESCE(SUM(CASE WHEN cust_repay_state = 'NORMAL' THEN amount END), 0) AS debt_need_finishall,
            COALESCE(DATEDIFF(CURDATE(), MAX(CASE WHEN cust_repay_state = 'REPAID' THEN act_repay_time END)), 0) AS repay_lst_repaid_days
        FROM repay_plan WHERE user_id = :userId GROUP BY user_id
        """)
    Map<String, Object> findUserDebtSummary(@Param("userId") String userId);

}
