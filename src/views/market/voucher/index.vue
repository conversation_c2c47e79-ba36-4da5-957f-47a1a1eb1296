<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="优惠券ID" prop="id" width="150px">
        <el-input v-model="queryParams.id" clearable size="small" />
      </el-form-item>
      <el-form-item label="优惠券名称" prop="couponName" width="150px">
        <el-input v-model="queryParams.couponName" clearable size="small" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" round size="mini" @click="handleQuery">搜索</el-button>
        <el-button type="success" round size="mini" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="优惠券ID" prop="id" align="center"></el-table-column>
      <el-table-column label="优惠券名称" prop="couponName" align="center"></el-table-column>
      <el-table-column label="优惠券类型" prop="couponType" align="center"></el-table-column>
      <el-table-column label="有效期(天)" prop="validDays" align="center"></el-table-column>
      <!-- <el-table-column label="发放类型" prop="sendType" align="center"></el-table-column> -->
      <el-table-column label="使用渠道" prop="applicableSystems" align="center">
        <template slot-scope="scope">
          <span>{{ formatApplicableSystems(scope.row.applicableSystems) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="抵扣期数" prop="freePeriodType" align="center" :formatter="freePeriodTypeFormat"></el-table-column>
      <el-table-column label="自定义期数" prop="freePeriod" align="center"></el-table-column>
      <el-table-column label="用户类型" prop="userType" align="center" :formatter="userTypeFormat"></el-table-column>

      <el-table-column label="修改时间" prop="updatedTime" align="center"></el-table-column>
      <el-table-column label="修改人" prop="updatedBy" align="center"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="hanleEdite(scope.row)">编辑</el-button>
          <el-button type="text" style="color: #c00;" @click="hanleDel(scope.row)">删除</el-button>
          <!-- <el-button type="text" @click="hanleSend(scope.row)">发券</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 新增 -->
    <el-dialog :title="title" :visible.sync="visible" width="500px" :before-close="reset">
      <el-form label-position="right" label-width="100px" :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item label="优惠券名称" prop="couponName">
          <el-input v-model="ruleForm.couponName"></el-input>
        </el-form-item>
        <el-form-item label="优惠券备注" prop="couponRemark">
          <el-input v-model="ruleForm.couponRemark"></el-input>
        </el-form-item>
        <el-form-item label="使用渠道" prop="applicableSystems">
          <el-checkbox-group v-model="ruleForm.applicableSystems">
            <el-checkbox v-for="item in applicableSystemsOptions" :label="item.name" name="applicableSystems">{{
              item.desc
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="优惠券类型" prop="couponType">
          <el-select @change="changeCouponType" v-model="ruleForm.couponType" placeholder="请选择" clearable style="width: 100%;">
            <el-option v-for="item in couponTypeOptions" :key="item.couponType" :label="item.typeName"
              :value="item.couponType" />
          </el-select>
        </el-form-item>
        <el-form-item label="额度(元)" prop="amount"
          v-if="['CREDIT_INCREASE', 'INTEREST_FREE_COUPON'].includes(ruleForm.couponType)">
          <el-input v-model="ruleForm.amount"></el-input>
        </el-form-item>
        <el-form-item label="折扣(折)" prop="discount" v-if="['RIGHTS_DISCOUNT'].includes(ruleForm.couponType)">
          <el-input v-model="ruleForm.discount"></el-input>
        </el-form-item>
        <!-- <el-form-item label="发放类型" prop="sendType">
          <el-radio-group v-model="ruleForm.sendType">
            <el-radio label="AUTOMATIC">系统自动</el-radio>
            <el-radio label="MANUAL">人工手动</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="有效期(天)" prop="validDays">
          <el-input v-model="ruleForm.validDays" placeholder="发放后N天后过期"></el-input>
        </el-form-item>

        <template>
          <el-form-item label="适用期数" prop="applicableTerms">
            <el-input v-model="ruleForm.applicableTerms" placeholder="多期英文逗号隔开"></el-input>
          </el-form-item>
        </template>
        
        <el-form-item label="抵扣期数" prop="freePeriodType" v-if="['INTEREST_FREE_COUPON'].includes(ruleForm.couponType)">
          <el-select v-model="ruleForm.freePeriodType" placeholder="请选择" clearable style="width: 100%;">
            <el-option v-for="item in freePeriodTypeOptions" :key="item.freePeriod" :label="item.typeName"
              :value="item.freePeriod" />
          </el-select>
        </el-form-item>
        <el-form-item label="自定义期数" prop="freePeriod" v-if="['INTEREST_FREE_COUPON'].includes(ruleForm.couponType) && ruleForm.freePeriodType === 'OTHERS'">
          <el-input v-model="ruleForm.freePeriod" placeholder="请填写"></el-input>
        </el-form-item>

        <el-form-item label="适用额度(元)" style="margin-bottom: 0;">
          <el-row>
            <el-col :span="11">
              <el-form-item prop="minAmount">
                <el-input v-model="ruleForm.minAmount" placeholder="最低"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="2" style="display: flex; justify-content: center;">-</el-col>
            <el-col :span="11">
              <el-form-item prop="maxAmount">
                <el-input v-model="ruleForm.maxAmount" placeholder="最高"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        
        <template v-if="['INTEREST_FREE_COUPON'].includes(ruleForm.couponType)">
          <el-form-item label="适用人群" prop="userType">
            <el-select v-model="ruleForm.userType" placeholder="请选择" clearable style="width: 100%;">
              <el-option v-for="item in userTypeOptions" :key="item.userType" :label="item.typeName"
                :value="item.userType" />
            </el-select>
          </el-form-item>
        </template>
        

        <el-form-item label="详细说明" prop="usageInstructions">
          <el-input v-model="ruleForm.usageInstructions" type="textarea" :rows="4"></el-input>
        </el-form-item>
        <!-- <el-form-item label="是否弹窗" prop="isPopup">
            <el-radio-group v-model="ruleForm.isPopup">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="2">否</el-radio>
            </el-radio-group>
          </el-form-item> -->
        <el-form-item>
          <el-button round type="primary" @click="submitForm('ruleForm')">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  query,
  saveCoupon,
  updateCoupon,
  deleteCoupon,
  queryCouponType,
  getCouponFlowConfig,
  queryCouponFreePeriod,
  queryUserType,
} from "@/api/marketing";

export default {
  name: "",
  data() {
    return {
      // 查询参数
      queryParams: {
        id: undefined,
        couponName: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      list: [],
      total: 0,

      couponTypeOptions: [],

      visible: false,
      title: '',

      applicableSystemsOptions: [],

      ruleForm: {
        couponType: 'CREDIT_INCREASE',
        applicableSystems: [],
        // sendType: 'AUTOMATIC',
        discount: 9,
        userType: 'ALL',
      },
      rules: {
        couponName: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { max: 10, message: '最多10个字', trigger: 'blur' },
        ],
        couponType: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
        applicableSystems: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
        validDays: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if ((value > 0)) {
                callback();
              } else {
                callback(new Error('请输入大于0的数字'));
              }
            },
            trigger: 'blur'
          }
        ],
        usageInstructions: [
          { max: 100, message: '最多100个字', trigger: 'blur' },
        ],
        couponRemark: [
          { max: 20, message: '最多20个字', trigger: 'blur' },
        ],
        discount: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if ((value >= 5 && value <= 9.9)) {
                callback();
              } else {
                callback(new Error('请输入5-9.9的数字'));
              }
            },
            trigger: 'blur'
          }
        ],
        amount: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value || (value >= 1 && value <= 100000)) {
                callback();
              } else {
                callback(new Error('请输入1到100000的数字'));
              }
            },
            trigger: 'blur'
          }
        ],
        minAmount: [
          {
            validator: (rule, value, callback) => {
              if (!value || (value >= 1 && value <= 1000000)) {
                callback();
              } else {
                callback(new Error('请输入1到1000000的数字'));
              }
            },
            trigger: 'blur'
          }
        ],
        maxAmount: [
          {
            validator: (rule, value, callback) => {
              if (!value || (value >= 1 && value <= 1000000)) {
                callback();
              } else {
                callback(new Error('请输入1到1000000的数字'));
              }
            },
            trigger: 'blur'
          }
        ],
        applicableTerms: [
          {
            validator: (rule, value, callback) => {
              const regExp = /^[0-9,]*$/;
              if (value && !regExp.test(value)) {
                callback(new Error('只能包含正整数和英文逗号'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        freePeriod: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              let v = Number(value)
              if (!Number.isInteger(v) || v < 1 || v > 12) {
                callback(new Error('只能输入1-12的整数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        freePeriodType: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
      },
      freePeriodTypeOptions: [],
      userTypeOptions: [],
    };
  },
  created() {
    this.getList()

    // 优惠券类型获取
    queryCouponType().then(res => {
      this.couponTypeOptions = res.data
    })

    // 流量方
    getCouponFlowConfig().then(res => {
      this.applicableSystemsOptions = res.data
    })

    // 抵扣期数
    queryCouponFreePeriod().then(res => {
      this.freePeriodTypeOptions = res.data
    })

    // 用户类型
    queryUserType().then(res => {
      this.userTypeOptions = res.data
    })
  },
  methods: {
    changeCouponType(e){
      if(e === 'INTEREST_FREE_COUPON'){
        this.ruleForm.userType = 'ALL'
      }
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 获取列表
    getList() {
      this.loading = true;
      query(this.queryParams).then(res => {
        this.list = res.data.list;
        this.total = res.data.total;
      }).finally(() => {
        this.loading = false;
      })
    },

    // 重置表单
    reset() {
      this.visible = false
      this.$refs['ruleForm'].resetFields();
      this.ruleForm = {
        couponType: 'CREDIT_INCREASE',
        applicableSystems: [],
        // sendType: 'AUTOMATIC',
        discount: 9,
      }
    },

    // 新增
    handleAdd() {
      this.title = '创建优惠券'
      this.visible = true
    },

    // 编辑
    hanleEdite(row) {
      this.ruleForm = {
        ...row,
        couponType: row.couponTypeEnum,
        applicableSystems: row.applicableSystems.split(","),
      }
      this.title = '修改优惠券'
      this.visible = true
    },

    // 保存
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.minAmount && this.ruleForm.maxAmount) {
            if (Number(this.ruleForm.maxAmount) <= Number(this.ruleForm.minAmount)) {
              this.$message.error('适用额度最高金额必须大于最低金额')
              return
            }
          }

          let params = {
            ...this.ruleForm,
            validDays: Number(this.ruleForm.validDays),
            amount: this.ruleForm.amount ? Number(this.ruleForm.amount) : undefined,
            minAmount: this.ruleForm.minAmount ? Number(this.ruleForm.minAmount) : undefined,
            maxAmount: this.ruleForm.maxAmount ? Number(this.ruleForm.maxAmount) : undefined,
            applicableSystems: this.ruleForm.applicableSystems.join(','),
            discount: this.ruleForm.discount ? Number(this.ruleForm.discount) : undefined,
          }

          if (['CREDIT_INCREASE', 'INTEREST_FREE_COUPON'].includes(this.ruleForm.couponType)) {
            delete params.discount
          }
          if (['RIGHTS_DISCOUNT'].includes(this.ruleForm.couponType)) {
            delete params.amount
          }

          if (!['INTEREST_FREE_COUPON'].includes(this.ruleForm.couponType)) {
            delete params.userType
            delete params.freePeriodType
          }

          if (!(['INTEREST_FREE_COUPON'].includes(this.ruleForm.couponType) && this.ruleForm.freePeriodType === 'OTHERS')) {
            delete params.freePeriod
          }

          if (this.ruleForm.id) {
            updateCoupon(params).then(() => {
              this.$message.success('修改成功')
              this.reset()
              this.getList()
            })
          } else {
            saveCoupon(params).then(() => {
              this.$message.success('创建成功')
              this.reset()
              this.getList()
            })
          }
        }
      });
    },

    // 删除
    hanleDel({ id }) {
      this.$confirm(`是否确认删除优惠券?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        roundButton: true,
        type: 'warning'
      }).then(() => {
        let params = [id]
        deleteCoupon(params).then(() => {
          this.getList()
        })
      })
    },

    // 格式化使用渠道
    formatApplicableSystems(string) {
      if (!string) return

      let res = ''
      let arr = string.split(',')
      let ToDesc = arr.map(item => {
        const obj = this.applicableSystemsOptions.find(x => x.name === item)
        return obj.desc
      })

      res = ToDesc.join()
      return res
    },

    freePeriodTypeFormat({freePeriodType}){
      let obj = this.freePeriodTypeOptions.find(item => item.freePeriod === freePeriodType)
      return obj ? obj.typeName : '--'
    },

    userTypeFormat({userType}){
      let obj = this.userTypeOptions.find(item => item.userType === userType)
      return obj ? obj.typeName : '--'
    },

    
  }
};
</script>
