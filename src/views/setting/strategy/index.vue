<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item
        label="状态"
        prop="strategyState"
        width="150px"
      >
        <el-select
          v-model="queryParams.strategyState"
          placeholder="请选择"
          clearable
        >
          <el-option
            label="启用"
            value="Y"
          />
          <el-option
            label="禁用"
            value="N"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          round
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          round
          size="mini"
          @click="handleResetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 10px;">
      <el-button
        type="primary"
        round
        size="mini"
        @click="handleAdd"
      >
        添加策略
      </el-button>
    </div>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      border="border"
      :data="dataSource"
    >
      <el-table-column
        label="策略名称"
        prop="strategyName"
        align="center"
      />
      <el-table-column
        label="策略编码"
        prop="strategyCode"
        align="center"
      />
      <el-table-column
        label="风控出额区间"
        prop="amount"
        align="center"
        :formatter="row => row.amountLower ? `${row.amountLower} - ${row.amountUpper}` : '-'"
      />
      <el-table-column
        label="状态"
        prop="strategyState"
        align="center"
        :formatter="row => row.strategyState === 'Y' ? '启用' : '禁用'"
      />
      <el-table-column
        label="修改时间"
        prop="modifyTime"
        align="center"
      />
      <el-table-column
        label="修改人"
        prop="operator"
        align="center"
      />
      <el-table-column
        label="操作"
        width="200px"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleConfig(scope.row)"
          >
            可用额度分箱配置
          </el-button>
          <el-button
            type="text"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            :style="{ color: scope.row.strategyState === 'Y' ? '#ff0000' : '#1a7efd' }"
            @click="handleToggleStatus(scope.row)"
          >
            {{ scope.row.strategyState === 'Y' ? '禁用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handleSearchList"
    />

    <!-- 新增 -->
    <el-dialog
      :title="mode === 'add' ? '添加策略' : '编辑策略'"
      :visible.sync="visible"
      width="500px"
      :before-close="handleClose"
    >
      <el-form
        ref="ruleForm"
        label-position="right"
        label-width="130px"
        :model="ruleForm"
        :rules="rules"
      >
        <el-form-item
          label="策略名称"
          prop="strategyName"
        >
          <el-input
            v-model="ruleForm.strategyName"
            placeholder="请填写"
          />
        </el-form-item>
        <el-form-item
          label="策略编码"
          prop="strategyCode"
        >
          <el-input
            v-model="ruleForm.strategyCode"
            :disabled="mode === 'edit'"
            placeholder="请填写"
          />
        </el-form-item>
        <el-form-item
          label="风控额度下限"
          prop="amountLower"
        >
          <el-input-number
            v-model="ruleForm.amountLower"
            style="width: 100%"
            controls-position="right"
            placeholder="请填写"
            :disabled="mode === 'edit'"
          />
        </el-form-item>
        <el-form-item
          label="风控额度上限"
          prop="amountUpper"
        >
          <el-input-number
            v-model="ruleForm.amountUpper"
            style="width: 100%"
            controls-position="right"
            placeholder="请填写"
            :disabled="mode === 'edit'"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            round
            @click="handleClose"
          >
            关闭
          </el-button>
          <el-button
            round
            type="primary"
            @click="handleSubmit"
          >
            保存
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      title="可用额度分箱"
      :visible="configVisible"
      width="500px"
      :before-close="handleConfigModalCancel"
    >
      <el-table
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          label="分箱编号"
          prop="binCode"
          width="80"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.relateCode || scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="前端可用额度"
          prop="frontAmount"
        >
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.frontAmount"
              size="small"
              controls-position="right"
              style="width: 100%"
              :precision="0"
              :min="0"
              :disabled="scope.row.id"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="分流比例 (%)"
          prop="ratio"
        >
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.ratio"
              size="small"
              controls-position="right"
              style="width: 100%"
              :precision="0"
              :min="0"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.id"
              size="text"
              style="color: #ff0000;"
              @click="removeRow(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="new-btn">
        <el-button
          type="text"
          size="mini"
          @click="addRow"
        >
          新增行
        </el-button>
      </div>

      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="handleConfigModalCancel">取 消</el-button>
        <el-button
          type="primary"
          @click="handleConfigModalConfirm"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryRevolvingStrategyList,
  queryRevolvingStrategyDetail,
  apiSaveRevolvingStrategy,
  apiRevolvingStrategyEnable,
  apiRevolvingStrategyDisable,
  apiSaveRevolvingStrategyRelate,
  queryRevolvingStrategyRelate
} from '@/api/setting/strategy'

export default {
  name: '',
  data() {
    return {
      // 查询参数
      queryParams: {
        strategyState: undefined,
        pageNum: 1,
        pageSize: 10
      },
      loading: false,
      dataSource: [],
      total: 0,

      selectedList: [], // 已选择列表
      visible: false,
      configVisible: false,
      mode: 'add',

      currentId: undefined,
      ruleForm: {
        strategyName: undefined,
        strategyCode: undefined,
        amountLower: undefined,
        amountUpper: undefined
      },
      rules: {
        strategyName: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
        strategyCode: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
        amountUpper: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
        amountLower: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
      },
      tableData: [{
        frontAmount: 0,
        ratio: 0
      }]
    }
  },
  created() {
    this.handleSearchList()
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.handleSearchList()
    },
    handleResetQuery() {
      this.$refs['queryForm'].resetFields()

      this.queryParams = {
        ...this.queryParams,
        paymentChannelCode: undefined,
        enabled: undefined,
        pageNum: 1
      }

      this.handleSearchList()
    },
    // 获取列表
    handleSearchList() {
      this.loading = true
      queryRevolvingStrategyList(this.queryParams).then(res => {
        this.dataSource = res.data.list
        this.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },

    handleClose() {
      this.visible = false

      this.$refs['ruleForm'].resetFields()
      this.ruleForm = {
        strategyName: undefined,
        strategyCode: undefined,
        amountLower: undefined,
        amountUpper: undefined
      }

      this.currentId = undefined;
    },

    // 新增
    handleAdd() {
      this.mode = 'add'
      this.visible = true
    },

    // 禁用/启用
    handleToggleStatus({ id, strategyState }) {
      const enabledFlag = strategyState === 'Y'
      this.$confirm(`是否确认${enabledFlag ? '禁用' : '启用'}该策略?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        roundButton: true,
        type: 'warning'
      }).then(() => {
        if (enabledFlag) {
          apiRevolvingStrategyDisable({ id }).then(() => {
            this.$message.success('禁用成功')
            this.handleSearchList()
          })
        } else {
          apiRevolvingStrategyEnable({ id }).then(() => {
            this.$message.success('启用成功')
            this.handleSearchList()
          })
        }
      })
    },
    // 编辑
    handleEdit(row) {
      queryRevolvingStrategyDetail({ id: row.id }).then(res => {
        this.ruleForm = {
          strategyName: res.data.strategyName,
          strategyCode: res.data.strategyCode,
          amountLower: res.data.amountLower,
          amountUpper: res.data.amountUpper
        }

        this.currentId = row.id;
        this.mode = 'edit';
        this.visible = true
      })
    },

    handleConfig(row) {
      queryRevolvingStrategyRelate({ strategyId: row.id }).then(res => {
        if (res.data.length) {
          this.tableData = res.data
        } else {
          this.tableData = [{
            frontAmount: 0,
            ratio: 0
          }]
        }

        this.currentId = row.id;
        this.configVisible = true;
      })

    },

    handleConfigModalConfirm() {
      const totalRatio = this.tableData.reduce((sum, item) => sum + item.ratio, 0);
      if (totalRatio !== 100) {
        this.$message.error('分流比例相加必须等于 100%')
        return;
      }

      const uniqueAmounts = new Set(this.tableData.map(item => item.frontAmount));
      if (uniqueAmounts.size !== this.tableData.length) {
        this.$message.error('额度值不能重复')

        return;
      }

      this.$confirm('确认后，前端可用额度不可修改！', '二次确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        apiSaveRevolvingStrategyRelate({
          strategyId: this.currentId,
          relateDetail: this.tableData.map((item, index) => ({
            ...item,
            relateCode: item.relateCode || (index + 1).toString()
          }))
        }).then(() => {
          this.$message.success('保存成功')
          this.handleConfigModalCancel()
          this.handleSearchList()
        })
      })
    },
    handleConfigModalCancel() {
      this.configVisible = false;
      this.currentId = false;

      this.tableData = [{
        frontAmount: 0,
        ratio: 0
      }]
    },

    // 保存
    handleSubmit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          apiSaveRevolvingStrategy({ ...this.ruleForm, id: this.currentId }).then(() => {
            this.$message.success(this.mode === 'add' ? '创建成功' : '编辑成功')
            this.handleClose()
            this.handleSearchList()
          })
        }
      })
    },
    addRow() {
      this.tableData.push({
        frontAmount: 0,
        ratio: 0
      })
    },
    removeRow(index) {
      this.tableData.splice(index, 1)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.new-btn {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-bottom: #e5e5e5 1px solid;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
