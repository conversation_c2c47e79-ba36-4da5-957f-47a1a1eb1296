<template>
  <div class="app-container">
    <div style="display: flex; align-items: center; margin-bottom: 16px;">
      <img src="@/assets/images/c_logo.png" style="height: 46px; width: auto; margin-right: 10px; border: #ccc 1px solid; border-radius: 10px;" />
      <span style="font-size: 18px;">运营管理</span>
    </div>
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item>
        <el-select placeholder="手机号" v-model="queryParams.type" style="width: 100px">
          <el-option label="手机号" value="mobile"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" clearable v-model="queryParams.mobile" style="width: 290px" />
      </el-form-item>
      <el-form-item>
        <el-button round type="primary" icon="el-icon-search" size="mini">查询</el-button>
      </el-form-item>
    </el-form>

    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="订单详情" name="first"> </el-tab-pane>
      <el-tab-pane label="投诉/备注" name="second"> </el-tab-pane>
      <el-tab-pane label="短信记录" name="third"> </el-tab-pane>
    </el-tabs>

    <el-table border="border" :data="list">
      <el-table-column label="订单编号" prop="c1" align="center">
        <div class="in" contenteditable="true">OR2408072356345748275505996677</div>
      </el-table-column>
      <el-table-column label="手机号" align="center">
        <div class="in" contenteditable="true">请输入</div>
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <div class="in" contenteditable="true">请输入</div>
      </el-table-column>
      <el-table-column label="身份证" align="center">
        <div class="in" contenteditable="true">请输入</div>
      </el-table-column>
      <el-table-column label="期数" align="center">
        <div class="in" contenteditable="true">6</div>
      </el-table-column>
      <el-table-column label="利率" align="center">
        <div class="in" contenteditable="true">0.3598</div>
      </el-table-column>
      <el-table-column label="审批金额" align="center">
        <div class="in" contenteditable="true">4000</div>
      </el-table-column>
      <el-table-column label="申请金额" align="center">
        <div class="in" contenteditable="true">4000</div>
      </el-table-column>
      <el-table-column label="放款金额" align="center">
        <div class="in" contenteditable="true">4000</div>
      </el-table-column>
      <el-table-column label="权益金额" align="center">
        <div class="in" contenteditable="true">499</div>
      </el-table-column>
      <el-table-column label="进件渠道" align="center">
        <div class="in" contenteditable="true">融360</div>
      </el-table-column>
      <el-table-column label="订单状态" align="center">
        <div class="in" contenteditable="true">放款通过</div>
      </el-table-column>
      <el-table-column label="申请日期" align="center">
        <div class="in" contenteditable="true">2024-08-07 23:56:35</div>
      </el-table-column>
      <el-table-column label="要款日期" align="center">
        <div class="in" contenteditable="true">2024-08-07 23:56:35</div>
      </el-table-column>
      <el-table-column label="放款日期" align="center">
        <div class="in" contenteditable="true">2024-08-19 07:01:15</div>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "",
  data() {
    return {
      activeName: "first",
      queryParams: {
        type:'mobile',
        mobile: "",
      },
      list: [
        {
          c1: "",
        },
      ],
    };
  },
  created() { },
  methods: {},
};
</script>

<style scoped>
.in {
  padding: 10px;
}
</style>
