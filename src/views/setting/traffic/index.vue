<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true" size="mini" >
      <el-form-item
        label="流量方渠道"
        width="150px"
      >
        <el-select
          v-model="queryParams.flowChannel"
          placeholder="请选择"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in dict.flowChannel"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          round
          size="mini"
          @click="handleQuery"
        >
          查询
        </el-button>
        <el-button
          round
          size="mini"
          @click="handleReset"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="流量方渠道" align="center">
        <template slot-scope="scope">
          <dict-tag :value="scope.row.flowChannel" :options="dict.flowChannel" />
        </template>
      </el-table-column>

      <el-table-column label="渠道编码" prop="flowChannel" align="center" />

      <el-table-column label="绑卡渠道" align="center">
        <template slot-scope="scope">
          <dict-tag :value="scope.row.firstProtocolChannel" :options="dict.protocolChannel" />
          <span v-if="scope.row.secondProtocolChannel">,</span>
          <dict-tag :value="scope.row.secondProtocolChannel" :options="dict.protocolChannel" />
        </template>
      </el-table-column>

      <el-table-column label="更新人" prop="updatedBy" align="center" />

      <el-table-column label="更新时间" prop="updatedTime" align="center" />

      <el-table-column label="操作" fixed="right" width="150" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="detail(scope.row.flowChannel)">查看</el-button>
          <el-button type="text" @click="edite(scope.row.flowChannel)" >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->

    <!-- 编辑查看渠道绑卡 -->
    <el-dialog :title="title" :visible.sync="visible" width="800px" :before-close="close">
      <el-form
        label-position="left"
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        inline
        :disabled="mode === 'detail'"
      >
        <el-form-item label="流量方渠道">
          <el-select v-model="ruleForm.flowChannel" disabled placeholder="请选择" style="width: 200px;">
            <el-option
              v-for="item in dict.flowChannel"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道编码">
          <el-input v-model="ruleForm.flowChannel" disabled style="width: 200px;" placeholder="渠道编码" />
        </el-form-item>
        <el-form-item label="第一绑卡渠道" prop="firstProtocolChannel">
          <el-select v-model="ruleForm.firstProtocolChannel" @change="ruleForm.secondProtocolChannel = ''" placeholder="请选择" style="width: 200px;">
            <el-option
              v-for="item in dict.protocolChannel"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="第二绑卡渠道" prop="secondProtocolChannel">
          <el-select v-model="ruleForm.secondProtocolChannel" @change="$forceUpdate()" placeholder="请选择" style="width: 200px;" clearable>
            <el-option
              v-for="item in dict.protocolChannel"
              :disabled="item.value === ruleForm.firstProtocolChannel"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          style="display: flex; justify-content: flex-end; padding-top: 20px;"
          v-if="mode !== 'detail'"
        >
          <el-button round @click="close">取 消</el-button>
          <el-button round type="primary" @click="submit">确 定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFlowProtocolChannelList,
  getFlowProtocolChannelDetail,
  saveFlowProtocolChannelDetail
} from "@/api/setting/traffic";
export default {
  name: "Traffic",
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 999
      },
      title: '编辑',
      total: 0,
      loading: false,
      visible: false,
      list: [],
      mode: "",
      ruleForm: {},
      isExist:false,
      rules: {
        firstProtocolChannel: [
          { required: true, message: "第一绑卡渠道不能为空", trigger: "change" }
        ]
      },
    };
  },
  dicts: ["flowChannel", "protocolChannel"],
  created() {

    this.getList();
  },

  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList();
    },
    handleReset() {
      this.resetQuery();
      this.handleQuery();
    },
    resetQuery() {
      this.queryParams = {};
      this.resetForm("queryForm");
    },

    // 获取列表
    getList() {
      this.loading = true;
      getFlowProtocolChannelList(this.queryParams).then(res => {
        this.list = res.data;
        this.total = res.data.total || this.list.length;
        this.loading = false;
      });
    },

    submit() {
      if(this.isExist){
        return
      }
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {

          let params = {
            ...this.ruleForm
          };

          console.log(params);

          saveFlowProtocolChannelDetail(params).then(res => {
            this.close();
            this.getList();
          });
        }
      });
    },

    // 关闭
    close() {
      this.visible = false;
      this.mode = "";
      this.$refs["ruleForm"].resetFields();
      this.ruleForm = {};
      this.isExist = false
    },

    detail(flowChannel) {
      getFlowProtocolChannelDetail({ flowChannel }).then(res => {
        let data = res.data;

        this.ruleForm = {
          ...data
        };

        this.mode = "detail";
        this.visible = true;
        this.title = "查看";
      });
    },

    edite(flowChannel) {
      getFlowProtocolChannelDetail({ flowChannel }).then(res => {
        let data = res.data;

        this.ruleForm = {
          ...data
        };

        this.mode = "edite";
        this.visible = true;
        this.title = "编辑";
      });
    }
  }
};
</script>
