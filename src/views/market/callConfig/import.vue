<template>
  <div class="app-container">
    <el-steps :active="1" style="margin-bottom: 16px;">
      <el-step title="制定案件筛选条件" description=""></el-step>
      <el-step title="预览/保存" description=""></el-step>
    </el-steps>

    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" :inline="true" label-width="160px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="话术" prop="">
            <el-select placeholder="请选择">

            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="当日重播次数" prop="">
            <el-select placeholder="请选择">

            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当日重播间隔" prop="">
            <el-select placeholder="请选择">

            </el-select>
            <span style="margin-left: 10px;">分钟</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="客户当日推送次数上限" prop="">
            <el-select placeholder="请选择">

            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="推送第三方" prop="">
            <el-select placeholder="请选择" style="margin-right: 10px;">

            </el-select>
            <el-input placeholder="输入比例" style="width: 100px;"></el-input>
            <span style="margin-left: 10px; margin-right: 30px;">%</span>

            <el-button type="primary" plain icon="el-icon-plus" size="small" circle></el-button>
            <el-button type="primary" plain icon="el-icon-minus" size="small" circle></el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item style="padding-left: 160px; padding-top: 30px;">
        <el-button round @click="">返回</el-button>
        <el-button round type="primary" @click="submitForm">提交</el-button>
      </el-form-item>
    </el-form>

    <el-dialog title="信息确认" :visible.sync="visible" width="800px" :before-close="close">
      <el-form :inline="true" label-width="160px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="话术" prop="">
              xxxx
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="策略名称" prop="">
              导入
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="当日重播次数" prop="">
              xxx
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当日重播间隔" prop="">
              xxx
              <span style="margin-left: 10px;">分钟</span>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="客户当日推送次数上限" prop="">
              xxx
            </el-form-item>
          </el-col>
        </el-row>


        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="推送第三方" prop="">
              xxx
              <span style="margin-left: 40px;">xxxx</span>
              <span style="margin-left: 10px; margin-right: 30px;">%</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="导入数据量" prop="">
              1421条
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item style="padding-left: 160px; padding-top: 30px;">
          <el-button round @click="">取消</el-button>
          <el-button round type="primary" @click="submitForm">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// import { rightsQuery, rightsDownload } from "@/api/rights";
import { get as getDictByName } from "@/api/system/dictDetail"

export default {
  name: "",
  data() {
    return {
      loading: false,
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dataVal: [],
      pickerOptionsDefault: {
        onPick: ({ maxDate, minDate }) => {
          //当我们选择两个值的时候，就认为用户已经选择完毕
          if (maxDate != null && minDate != null) {
            this.repayDateBegin = maxDate;
            this.repayDateEnd = minDate;
          }
        },
        disabledDate: (time) => {
          let maxDate = this.maxDate;
          let minDate = this.minDate;
          if (maxDate != null && minDate != null) {
            let days = maxDate.getTime() - minDate.getTime();
            //计算完之后必须清除，否则选择器一直处于禁止选择的状态
            this.maxDate = null;
            this.minDate = null;
            return parseInt(days / (1000 * 60 * 60 * 24)) > 30;
          } else {
            //设置当前时间后的时间不可选
            return time.getTime() > Date.now();
          }
        },
      },
      visible: true,
    };
  },
  created() {

  },
  methods: {

  },
};
</script>
