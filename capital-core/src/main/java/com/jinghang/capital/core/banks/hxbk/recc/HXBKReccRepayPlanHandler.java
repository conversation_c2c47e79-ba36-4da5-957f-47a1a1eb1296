package com.jinghang.capital.core.banks.hxbk.recc;

import com.jinghang.capital.core.banks.hxbk.enums.HXBKReccFileType;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.entity.hxbk.HXBKReccRepay;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 还款合约明细文件处理
 * @作者 Mr.sandman
 * @时间 2025/07/10 18:20
 */
@Component
public class HXBKReccRepayPlanHandler extends HXBKReccAbstractHandler {

  private static final Logger logger = LoggerFactory.getLogger(HXBKReccRepayPlanHandler.class);
  /**
   * 对账处理
   *
   * @param reccDay 对账文件日期
   */
  @Override
  public void process( LocalDate reccDay ) {
    //对资正常还款，非代偿
    List<BankRepayRecord> repayRecords = findSuccessBankRepayRecordNormal(reccDay, false);
    logger.info("对资正常还款，非代偿业务数量:{}", repayRecords.size());
    CYBKReconcileFile reconcileFile = findReconcileFile(reccDay, HXBKReccFileType.REPAYMENT_DETAIL_FILE);
    reconcileFile.setReccDate(LocalDate.now());
    String reccId = reconcileFile.getId();
    List<HXBKReccRepay> records = findReccRepayDetailFileRecords(reccId);
    List<HXBKReccRepay> reccRepayRecords = records.stream().filter(lf -> !"05".equals(lf.getRepayType())).toList();
    logger.info("湖消还款明细数量:{}", reccRepayRecords.size());

    if (repayRecords.size() == 0 && reccRepayRecords.size() == 0) {
      reconcileFile.setReccState(ReccStateEnum.S.name());
      updateCYBKReconcileFile(reconcileFile);
      return;
    }

    if (repayRecords.size() != reccRepayRecords.size()) {
      logger.warn("湖消还款明细对账成功条数不一致 reccType：{} reccDay：{} 业务方条数：{} 资方条数：{}", HXBKReccFileType.REPAYMENT_DETAIL_FILE, reccDay, repayRecords.size(),
                  reccRepayRecords.size());
      getWarningService().warn("\n湖消还款明细对账:" + HXBKReccFileType.REPAYMENT_DETAIL_FILE + "\n对账日:" + reccDay + "\n成功条数不一致 ");
    }

    List<HXBKReccRepay> successList = new ArrayList<>();
    reccRepayRecords.forEach(lf -> {
      var loanId = lf.getSysId();
      var existRecord = filterRepayInter(repayRecords, loanId, lf.getSeqNo());
      boolean match = match(lf, existRecord);
      lf.setReccStatus(match ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
      if (match) {
        successList.add(lf);
      } else {
        //对账失败打印日志
        warningLog(lf, existRecord, loanId);
      }
      updateReccRepay(lf);
    });
    boolean allMatch = successList.size() == reccRepayRecords.size() && successList.size() == repayRecords.size();
    reconcileFile.setReccState(allMatch ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
    updateCYBKReconcileFile(reconcileFile);
    //对账失败，企业微信告警
    if (ReccStateEnum.F.name().equals(reconcileFile.getReccState())) {
      getWarningService().warn("\n湖消还款明细对账失败:" + HXBKReccFileType.REPAYMENT_DETAIL_FILE + "\n对账日:" + reccDay + "\n，对账成功总数与系统还款总数不一致 ");
    }

  }

  private boolean match(HXBKReccRepay repay, BankRepayRecord repayRecord) {
    if (repayRecord == null) {
      return false;
    }
    if ( ProcessStatus.SUCCESS != repayRecord.getRepayStatus()) {
      return false;
    }

    // TODO 这里实际开发联调阶段在具体看一下真实金额的计算
    // 系统 本金+利息+罚息+融担费
    BigDecimal sysReduceAmt = repayRecord.getPrincipalAmt().add(repayRecord.getInterestAmt()).add(repayRecord.getPenaltyAmt()).add(repayRecord.getGuaranteeAmt());
    logger.info("系统 本金+利息+罚息+融担费:{}", sysReduceAmt);
    // 资方 本金+利息+罚息+融担费
    BigDecimal bankReduceAmt = getAmtInYuan(repay.getPaidPrinAmt()).add(getAmtInYuan(repay.getPaidIntAmt())
                                                                            .add(repay.getPaidOvdPrinPnltAmt()).add(repay.getFeeAmt()));
    logger.info("资方 本金+利息+罚息+融担费:{}", bankReduceAmt);

    //资方的总金额里不包含融担费
    BigDecimal sysTotalAmount = repayRecord.getTotalAmt().subtract(repayRecord.getGuaranteeAmt());
    logger.info("系统总金额:{}", sysTotalAmount);
    BigDecimal repayAmt = getAmtInYuan(repay.getRepayAmt());
    logger.info("资方总金额:{}", repayAmt);
    return sysTotalAmount.compareTo(repayAmt) == 0
        && sysReduceAmt.compareTo(bankReduceAmt) == 0;
  }

  private BankRepayRecord filterRepayInter(List<BankRepayRecord> recordList, String loanId, String seqNo) {
    return recordList.stream().filter(record -> record.getLoanId().equals(loanId) && seqNo.equals(record.getBankSerial())).findAny().orElse(null);
  }

  private void warningLog( HXBKReccRepay lf, BankRepayRecord existRecord, String loanId ) {
    BigDecimal sysAmount = null;
    Integer sysPeriod = null;
    if ( Objects.nonNull(existRecord)) {
      sysAmount = existRecord.getTotalAmt();
      sysPeriod = existRecord.getPeriod();
    }
    logger.warn("长银直连对账失败，reccType：{} 资方loanId：{}，还款总金额:{}; 业务方总金额:{},期数:{}",
                HXBKReccFileType.REPAYMENT_DETAIL_FILE, loanId, getAmtInYuan(lf.getRepayAmt()), sysAmount, sysPeriod);
  }

  // 传入金额参数 返回元为单位的参数
  private BigDecimal getAmtInYuan( BigDecimal Amt ) {
    return Amt.divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
  }

  @Override
  public ReccType getReccType() {
    return ReccType.REPAY;
  }
}
