package com.maguo.loan.cash.flow.service;

import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entrance.ppd.service.PpdService;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.util.Map;

@SpringBootTest(properties = {
    "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration"
})
class RiskServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(RiskServiceTest.class);
    @MockBean
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private RiskService riskService;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private UserInfoRepository userInfoRepository;
    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Test
    void testBuildParam() {
        String userId = "15815596366";
        Map<String, Object> userApplyAmountHistory = preOrderRepository.findUserApplyAmountHistory(userId, FlowChannel.PPCJDL.name(), "POR25070115492663851753982905648");
        Loan firstLoan = loanRepository.findTopByUserIdAndFlowChannelAndLoanStateOrderByApplyTime(userId, FlowChannel.PPCJDL, ProcessState.SUCCEED);
        Map<String, Object> userDebtSummary = repayPlanRepository.findUserDebtSummary(userId);
        Map<String, Object> userRepaySummary = orderRepository.findUserRepaySummary(userId);
        logger.info("userApplyAmountHistory:{}", JsonUtil.toJsonString(userApplyAmountHistory));
        logger.info("firstLoan:{}", JsonUtil.toJsonString(firstLoan));
        logger.info("firstLoanHour:{}", JsonUtil.toJsonString(firstLoan.getApplyTime().getHour()));
        logger.info("userDebtSummary:{}", JsonUtil.toJsonString(userDebtSummary));
        logger.info("userRepaySummary:{}", JsonUtil.toJsonString(userRepaySummary));

        UserInfo userInfo = userInfoRepository.findById(userId).orElseThrow();
        UserRiskRecord userRiskRecord = userRiskRecordRepository.findTopByUserIdOrderByCreatedTimeDesc(userId);
        logger.info("result:{}", JsonUtil.toJsonString(riskService.buildRiskLoanApplyRequest(userRiskRecord, userInfo)));
    }
}
