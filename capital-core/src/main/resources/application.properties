server.compression.enabled=true
server.port=8001
server.tomcat.threads.max=500
server.tomcat.threads.min-spare=20
server.tomcat.max-connections=10000
server.tomcat.accept-count=600
server.shutdown=graceful

logging.file.name=/usr/local/apps/logs/capital-core-service.log
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n
logging.logback.rollingpolicy.max-file-size=500MB
logging.logback.rollingpolicy.total-size-cap=1200MB
# 按天存储日志配置
logging.logback.rollingpolicy.file-name-pattern=/usr/local/apps/logs/capital-core-uat.stdout.%d{yyyy-MM-dd}.%i.log
logging.logback.rollingpolicy.max-history=30
logging.level.com.netflix.discovery=warn
# 屏蔽特定类的WARN日志，设置为ERROR级别，屏蔽长银API包里面的【---- 写超时 -----】
logging.level.com.cycfc.communicate.handler.AbstractChannelHandler=error

spring.application.name=capital-core-service
spring.config.import=apollo://
spring.jpa.open-in-view=false
spring.rabbitmq.listener.simple.acknowledge-mode=manual
spring.rabbitmq.listener.direct.acknowledge-mode=manual

spring.data.redis.repositories.enabled=false

eureka.instance.prefer-ip-address=true
eureka.instance.lease-expiration-duration-in-seconds=12
eureka.instance.lease-renewal-interval-in-seconds=4
eureka.client.registry-fetch-interval-seconds=5
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true

management.endpoints.web.exposure.include=health,serviceregistry


#logging.level.org.springframework.data.jpa=trace
#logging.level.org.springframework.jdbc=debug
#logging.level.org.hibernate.SQL=debug
#logging.level.org.hibernate.orm.jdbc.bind=trace
#logging.level.org.hibernate.orm.query=trace
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.tags-sorter=alpha
springdoc.swagger-ui.operations-sorter=alpha
springdoc.api-docs.path=/v3/api-docs
springdoc.group-configs[0].group=fin-core-service
springdoc.group-configs[0].paths-to-match=/**
springdoc.group-configs[0].packages-to-scan=com.jinghang.capital.core.controller
knife4j.setting.language=zh_cn
knife4j.enable=false

hxbk.server.baseUrl= https://apdevcenter.antchain.antgroup.com/developer/product/RISKPLUS?api=riskplus.dubbridge.usecredit.apply_1.0
hxbk.loan.start=2300
hxbk.loan.end=0300
#todo ???apollo
hxbk.server.ak=www.baidu.com
hxbk.server.sk=www.baidu.com
# rabbitmq-cn-rp64bkp6s11.cn-shanghai.amqp-32.vpc.mq.amqp.aliyuncs.com:5672

hxbk.returnfile.path.prefix=/download_uat/v2/repay/
# ??????sftp??
mayi.sftp.host = sftp-dev.alipay.com
mayi.sftp.username = loanplat
mayi.sftp.password = 123456
mayi.sftp.port = 22
# ??????
mayi.sftp.download.loan = /download_uat/v2/loan/
# ??????
mayi.sftp.download.repay = /download_uat/v2/repay/
# ????????
mayi.sftp.download.contract = /download_uat/contract/
# ??????????
mayi.sftp.download.idcard = /download/contract/idcard/
# ?????????
mayi.sftp.download.photo = /download/contract/photo/
