<template>
  <div class="app-container">
    <!-- 列表 -->
    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="业务主体" prop="businessSubject" align="center" :formatter="businessSubjectFormat"></el-table-column>
      <el-table-column label="收款主体" prop="collectSubject" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.collectSubject === 'ZHONG_QIE'">仲且</span>
          <span v-else-if="scope.row.collectSubject === 'YING_MIN'">瀛旻</span>
          <span v-else>{{ scope.row.collectSubject }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付宝账号" prop="aliAccount" align="center"></el-table-column>
      <el-table-column label="微信账号" prop="wechatAccount" align="center"></el-table-column>
      <el-table-column label="收款后转账主体" prop="transferAccount" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.transferAccount === 'YTX'">益通祥</span>
          <span v-if="scope.row.transferAccount === 'DFS'">鼎发顺</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button type="text" @click="handleEdite(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="500px">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-position="top">
        <el-form-item label="业务主体" prop="businessSubject">
          <!-- <el-input v-model="ruleForm.businessSubject" disabled></el-input> -->
          <el-select v-model="ruleForm.businessSubject" placeholder="请选择" style="width: 100%" disabled>
            <el-option v-for="(item, index) in businessSubjectOptions" :label="item.label" :value="item.value" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收款主体" prop="collectSubject">
          <el-select v-model="ruleForm.collectSubject" placeholder="请选择" style="width: 100%" :disabled="mode === 'view'">
            <el-option v-for="(item, index) in collectSubjectOptions" :label="item.label" :value="item.value" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="支付宝账号" prop="aliAccount">
          <el-input v-model="ruleForm.aliAccount" :disabled="mode === 'view'"></el-input>
        </el-form-item>
        <el-form-item label="微信账号" prop="wechatAccount">
          <el-input v-model="ruleForm.wechatAccount" :disabled="mode === 'view'"></el-input>
        </el-form-item>
        <el-form-item label="收款后转账主体" prop="transferAccount">
          <!-- <el-input v-model="ruleForm.transferAccount" disabled></el-input> -->
          <div style="display: flex; background: #f5f5f5; height: 32px; align-items: center; border-radius: 16px; border: #e5e5e5 1px solid; padding-left: 12px; font-size: 14px;">
            <span v-if="ruleForm.transferAccount === 'YTX'">益通祥</span>
            <span v-if="ruleForm.transferAccount === 'DFS'">鼎发顺</span>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button v-if="mode === 'edite'" round type="primary" @click="submit">保存</el-button>
          <el-button round @click="close">关闭</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  aggregatePayConfig,
  updateConfig
} from "@/api/postLoanManage";
import { get as getDictByName } from "@/api/system/dictDetail"

export default {
  name: "",
  data() {
    return {
      loading: false,
      list: [],

      dialogVisible: false,
      mode: '',
      title: '',
      ruleForm: {},
      rules: {},

      collectSubjectOptions: [
        { label: '仲且', value: 'ZHONG_QIE' },
        { label: '瀛旻', value: 'YING_MIN' }
      ],

      businessSubjectOptions:[],
    };
  },
  created() {
    getDictByName("productChannel").then(res => {
      this.businessSubjectOptions = res.content;
      this.getList();
    });
  },
  methods: {
    // 获取列表
    getList() {
      this.loading = true;
      aggregatePayConfig().then(res => {
        this.list = res.data;
        this.loading = false;
      });
    },

    // 查看
    handleView(row) {
      this.mode = 'view'
      this.title = '查看'
      this.ruleForm = { ...row }
      this.dialogVisible = true
    },

    // 修改
    handleEdite(row) {
      this.mode = 'edite'
      this.title = '修改'
      this.ruleForm = { ...row }
      this.dialogVisible = true
    },

    // 关闭
    close(){
      this.mode = ''
      this.title = ''
      this.ruleForm = { }
      this.dialogVisible = false
    },

    // 保存
    submit(){
      updateConfig(this.ruleForm).then(res => {
        this.$message.success('修改成功')
        this.close()
        this.getList()
      })
    },

    businessSubjectFormat({ businessSubject }) {
      let obj = this.businessSubjectOptions.find(item => item.value === businessSubject)
      return obj ? obj.label : '--'
    },
  }
};
</script>
