<template>
  <div class="app-container">
    <div style="margin-bottom: 10px;">
      <el-button type="primary" round size="mini" @click="handleAdd">添加</el-button>
    </div>
    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="序号" prop="" align="center"></el-table-column>
      <el-table-column label="话术中文名" prop="" align="center"></el-table-column>
      <el-table-column label="慧语话术ID" prop="" align="center"></el-table-column>
      <el-table-column label="风焱话术ID" prop="" align="center"></el-table-column>
      <el-table-column label="操作" prop="" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdite(scope.row)">修改</el-button>
          <el-button type="text" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 新增 -->
    <el-dialog :title="mode === 'add' ? '新增' : '编辑'" :visible.sync="visible" width="500px" :before-close="close">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item label="话术中文名" prop="name">
          <el-input />
        </el-form-item>
        <el-row :gutter="20" style="background: #f5f5f5; margin-bottom: 20px;">
          <el-col :span="12">
            <el-form-item label="慧语话术ID" prop="name">
              <el-input />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="话术ID关联的机器人" prop="name">
              <el-input />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="background: #f5f5f5; margin-bottom: 20px;">
          <el-col :span="12">
            <el-form-item label="风焱话术ID" prop="">
              <el-input />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="话术ID关联的机器人" prop="">
              <el-input />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item style="display: flex; justify-content: flex-end;">
          <el-button round @click="close">取消</el-button>
          <el-button round type="primary" @click="submitForm">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// import { rightsQuery, rightsDownload } from "@/api/rights";
import { get as getDictByName } from "@/api/system/dictDetail"

export default {
  name: "",
  data() {
    return {
      loading: false,
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 弹窗
      mode: '',
      visible: false,
      ruleForm: {},
      rules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      // this.loading = true;
      // if (this.dataVal && this.dataVal.length === 2) {
      //   this.queryParams.startDate = this.dataVal[0]
      //   this.queryParams.endDate = this.dataVal[1]
      // }
      // rightsQuery(this.queryParams).then((res) => {
      //   this.list = res.data.list;
      //   this.total = res.data.total;
      //   this.loading = false;
      // });
    },

    // 新增
    handleAdd() {
      this.mode = 'add'
      this.visible = true
    },

    // 关闭弹窗
    close() {
      this.visible = false;
      this.ruleForm = {}
      this.$refs['ruleForm'].resetFields();
    },

    // 提交
    submitForm() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          if (this.mode === 'add') {
            // addComplaint(this.ruleForm).then(res => {
            //   this.$message.success("新增投诉成功！");
            //   this.closeComplaint();
            //   this.getComplaintNote();
            // });
          } else if (this.mode === 'edite') {
            // updateComplaint(this.ruleForm).then(res => {
            //   this.$message.success("编辑投诉成功！");
            //   this.closeComplaint();
            //   this.getComplaintNote();
            // });
          }
        }
      });
    }
  },
};
</script>
