<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item
        label="权益商名称"
        prop="rightsSupplier"
        width="150px"
      >
        <el-select
          v-model="queryParams.rightsSupplier"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          round
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          round
          size="mini"
          @click="handleResetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-button
      round
      type="primary"
      size="mini"
      style="margin-bottom: 16px;"
      @click="add"
    >
      新增
    </el-button>

    <el-table
      v-loading="loading"
      border="border"
      :data="dataSource"
    >
      <el-table-column
        label="权益商名称"
        prop="rightsSupplierName"
        align="center"
        :formatter="row => row.rightsSupplier ? options.find(o => o.value === row.rightsSupplier).label : '-'" 
      />
      <el-table-column
        label="权益商编码"
        prop="rightsSupplier"
        align="center"
      />
      <el-table-column
        label="扣款通道优先级"
        prop="rightsPaymentStr"
        align="center"
      />
      <el-table-column
        label="修改时间"
        prop="updatedTime"
        align="center"
      />
      <el-table-column
        label="修改人"
        prop="updatedBy"
        align="center"
      />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleAction(scope.row, 'detail')"
          >
            查看
          </el-button>
          <el-button
            type="text"
            @click="handleAction(scope.row, 'edit')"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="700px"
      :before-close="handleClose"
      destroy-on-close
    >
      <el-form
        ref="modalForm"
        :model="modalForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="权益商名称"
              prop="rightsSupplier"
              :rules="modalFormRules.rightsSupplier"
            >
              <el-select
                v-model="modalForm.rightsSupplier"
                :disabled="mode !== 'add'"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="权益商编码"
              prop="rightsSupplier"
            >
              <el-input
                v-model="modalForm.rightsSupplier"
                :disabled="true"
                placeholder="请填写"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <TableForm
        v-model="selectedList"
        :mode="mode"
      />
      <span slot="footer">
        <el-button
          round
          @click="handleClose"
        >关闭</el-button>
        <el-button
          v-if="mode !== 'detail'"
          round
          type="primary"
          @click="handleModalSave"
        >保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { get as getDictByName } from '@/api/system/dictDetail'
import { saveRightsRouteConfig, queryRightsRouteConfigList, queryRightsRouteConfigDetail } from '@/api/setting/deduct'
import TableForm from './TableForm'

export default {
  name: '',
  components: {
    TableForm
  },
  data() {
    const validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('不能为空'))
      } else if (this.mode === 'add' && this.dataSource.find(i => i.rightsSupplier === value)) {
        callback(new Error('此权益商配置已存在'))
      } else {
        callback()
      }
    }

    return {
      queryParams: {
        rightsSupplier: undefined,
        pageNum: 1,
        pageSize: 999
      },
      options: [],

      loading: true,
      dataSource: [],

      mode: '',
      dialogVisible: false,
      title: '',
      modalForm: {
        rightsSupplier: undefined
      },
      modalFormRules: {
        rightsSupplier: [
          { validator: validateName, trigger: 'blur' }
        ]
      },
      selectedList: []
    }
  },
  created() {
    this.handleGetOptions()

    this.handleSearchList()
  },
  methods: {
    // 获取列表
    handleSearchList() {
      this.loading = true
      queryRightsRouteConfigList(this.queryParams).then(res => {
        this.dataSource = res.data
        this.loading = false
      })
    },

    handleGetOptions() {
      getDictByName('rightsSupplier').then(res => {
        this.options = res.content
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.handleSearchList()
    },
    handleResetQuery() {
      this.queryParams = {
        ...this.queryParams,
        rightsSupplier: undefined,
        pageNum: 1
      }

      this.handleSearchList()
    },

    // 新增弹窗
    add() {
      this.mode = 'add'
      this.title = '新增'
      this.dialogVisible = true
    },
    handleAction(row, mode) {
      this.mode = mode
      this.title = mode === 'detail' ? '查看' : '编辑'

      queryRightsRouteConfigDetail({ rightsSupplier: row.rightsSupplier }).then(res => {
        this.modalForm = {
          rightsSupplier: res.data.rightsSupplier
        }
        this.selectedList = res.data.paymentChannelList ? res.data.paymentChannelList.map(o => ({
          ...o,
          disabled: o.enabled === 'N'
        })) : []

        this.dialogVisible = true
      })
    },

    // 关闭新增、编辑弹窗
    handleClose() {
      this.dialogVisible = false

      this.title = ''
      this.flowId = undefined
      this.selectedList = []
      this.dropVisible = false
    },

    // 新增/编辑 保存
    handleModalSave() {
      this.$refs['modalForm'].validate(valid => {
        if (!valid) return

        if (this.selectedList.length === 0) {
          this.$message.error('请添加通道！')
          return
        }

        if (this.selectedList.some(o => !o.dailyUpperLimit)) {
          this.$message.error('请输入每日扣款笔数上限！')
          return
        }

        if (this.selectedList.some(o => o.dailyUpperLimit < 1)) {
          this.$message.error('每日扣款笔数上限不能小于1')
          return
        }

        if (this.selectedList.some(o => !/^\d+$/.test(o.dailyUpperLimit))) {
          this.$message.error('每日扣款笔数上限只能输入正整数')
          return
        }

        if (this.selectedList.some(o => (o.feeSubsidy === undefined || o.feeSubsidy === null))) {
          this.$message.error('请输入手续费补贴！')
          return
        }

        if (this.selectedList.some(o => (o.feeSubsidy && o.feeSubsidy.toString().split('.')[1] && o.feeSubsidy.toString().split('.')[1].length > 2))) {
          this.$message.error('手续费补贴最多保留两位小数！')
          return
        }

        if (this.selectedList.some(o => o.feeSubsidy < 0)) {
          this.$message.error('手续费补贴上限不能小于0')
          return
        }

        const params = {
          ...this.modalForm,
          paymentChannelList: this.selectedList.map((item, index) => ({
            id: item.id,
            paymentChannelCode: item.paymentChannelCode,
            dailyUpperLimit: item.dailyUpperLimit,
            feeSubsidy: item.feeSubsidy,
            enabled: item.disabled ? 'N' : 'Y',
            priority: index + 1
          }))
        }

        saveRightsRouteConfig({ ...params }).then(() => {
          this.$message.success(this.mode === 'add' ? '保存配置成功' : '编辑配置成功')
          this.handleClose()
          this.handleSearchList()
        })
      })
    }
  }
}
</script>

<style scoped>
.empty {
  font-size: 30px;
  color: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 500px;
}

.box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.box .label {
  margin-right: 16px;
}

.tip {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
}

.table-head {
  background: #f5f5f5;
  display: flex;
  align-items: center;
}

.table-head span {
  padding: 10px;
  font-size: 13px;
}

.table-head span:nth-child(1) {
  width: 20%;
}

.table-head span:nth-child(2) {
  width: 50%;
}

.table-head span:nth-child(3) {
  flex: 1;
}

.table-body .item {
  display: flex;
  align-items: center;
  border-bottom: #e5e5e5 1px solid;
  background: #fff;
}

.table-body .item:hover {
  background: #e5e5e5;
}

.table-body .item span:nth-child(1) {
  width: 20%;
  padding: 10px;
}

.table-body .item span:nth-child(2) {
  width: 50%;
  padding: 10px;
}

.item-btns {
  padding: 5px 10px;
  flex: 1;
  display: flex;
  align-items: center;
}

.new-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-bottom: #e5e5e5 1px solid;
  position: relative;
}

.drop {
  background: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 240px;
  top: 30px;
  padding: 6px 16px;
  border-radius: 10px;
  width: 180px;
}

.drop-list {
  padding: 5px 0;
}

.btn-box {
  display: flex;
  justify-content: flex-end;
  padding: 6px 0 6px 0;
}

.dis-tip {
  font-style: normal;
  font-size: 12px;
  background: #f5f5f5;
  display: inline-block;
  padding: 4px 4px;
  border-radius: 4px;
  border: #e5e5e5 1px solid;
  line-height: 1;
  margin-left: 8px;
}
</style>
